#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * Test Script for Enhanced Image Optimization
 * 
 * This script tests the image optimization functionality with sample images
 * and validates that the optimization meets the specified requirements.
 */

const fs = require('fs');
const path = require('path');
const { 
  optimizeToTargetSize, 
  getImageMetadata, 
  getFileSizeInKB 
} = require('./utils/image-optimizer');

/**
 * Test configuration
 */
const testConfig = {
  testDir: path.join(__dirname, 'test-images'),
  outputDir: path.join(__dirname, 'test-output'),
  targetSizeKB: 100,
  idealSizeKB: 80,
  testImages: [
    'test-large.jpg',
    'test-medium.png', 
    'test-small.webp'
  ]
};

/**
 * Create test directory and sample images if they don't exist
 */
async function setupTestEnvironment() {
  console.log('🔧 Setting up test environment...');
  
  // Create directories
  if (!fs.existsSync(testConfig.testDir)) {
    fs.mkdirSync(testConfig.testDir, { recursive: true });
  }
  
  if (!fs.existsSync(testConfig.outputDir)) {
    fs.mkdirSync(testConfig.outputDir, { recursive: true });
  }
  
  // Check if we have real images to test with
  const realImages = fs.readdirSync(path.join(process.cwd(), 'public', 'images'))
    .filter(file => ['.jpg', '.jpeg', '.png', '.webp'].includes(path.extname(file).toLowerCase()))
    .slice(0, 3); // Take first 3 images
  
  if (realImages.length === 0) {
    console.log('⚠️  No real images found in public/images for testing');
    console.log('   Please add some test images to public/images directory');
    return false;
  }
  
  console.log(`✅ Found ${realImages.length} images for testing`);
  return realImages.map(img => path.join(process.cwd(), 'public', 'images', img));
}

/**
 * Test image optimization with different parameters
 */
async function testOptimization(imagePath, testName) {
  console.log(`\n🧪 Testing: ${testName}`);
  console.log(`📁 Source: ${path.basename(imagePath)}`);
  
  const originalSize = getFileSizeInKB(imagePath);
  console.log(`📏 Original size: ${originalSize}KB`);
  
  if (originalSize === 0) {
    console.log('❌ Could not read image file');
    return false;
  }
  
  const metadata = await getImageMetadata(imagePath);
  if (!metadata) {
    console.log('❌ Could not get image metadata');
    return false;
  }
  
  console.log(`📐 Dimensions: ${metadata.width}x${metadata.height}`);
  console.log(`🎨 Format: ${metadata.format}`);
  
  // Test WebP optimization
  const webpOutputPath = path.join(testConfig.outputDir, `${testName}-optimized.webp`);
  const webpResult = await optimizeToTargetSize(imagePath, webpOutputPath, {
    targetSizeKB: testConfig.targetSizeKB,
    idealSizeKB: testConfig.idealSizeKB,
    format: 'webp'
  });
  
  if (webpResult.success) {
    console.log(`✅ WebP optimization successful:`);
    console.log(`   📦 Size: ${webpResult.optimizedSizeKB}KB (${webpResult.compressionRatio}% compression)`);
    console.log(`   🎯 Quality: ${webpResult.quality}%`);
    console.log(`   ⭐ Met target: ${webpResult.metTarget ? 'Yes' : 'No'}`);
    console.log(`   🌟 Met ideal: ${webpResult.metIdeal ? 'Yes' : 'No'}`);
  } else {
    console.log(`❌ WebP optimization failed: ${webpResult.error}`);
    return false;
  }
  
  // Test JPEG optimization
  const jpegOutputPath = path.join(testConfig.outputDir, `${testName}-optimized.jpg`);
  const jpegResult = await optimizeToTargetSize(imagePath, jpegOutputPath, {
    targetSizeKB: testConfig.targetSizeKB,
    idealSizeKB: testConfig.idealSizeKB,
    format: 'jpeg'
  });
  
  if (jpegResult.success) {
    console.log(`✅ JPEG fallback successful:`);
    console.log(`   📦 Size: ${jpegResult.optimizedSizeKB}KB (${jpegResult.compressionRatio}% compression)`);
    console.log(`   🎯 Quality: ${jpegResult.quality}%`);
  } else {
    console.log(`⚠️  JPEG optimization failed: ${jpegResult.error}`);
  }
  
  return true;
}

/**
 * Test responsive image generation
 */
async function testResponsiveGeneration(imagePath, testName) {
  console.log(`\n📱 Testing responsive generation for: ${testName}`);
  
  const { generateResponsiveSizes } = require('./utils/image-optimizer');
  
  const responsiveResults = await generateResponsiveSizes(imagePath, testConfig.outputDir, {
    sizes: [320, 640, 1024],
    targetSizeKB: testConfig.targetSizeKB,
    format: 'webp',
    suffix: testName
  });
  
  if (responsiveResults.length > 0) {
    console.log(`✅ Generated ${responsiveResults.length} responsive sizes:`);
    responsiveResults.forEach(result => {
      console.log(`   📐 ${result.width}w: ${result.optimizedSizeKB}KB (${result.compressionRatio}% compression)`);
    });
  } else {
    console.log('⚠️  No responsive sizes generated');
  }
  
  return responsiveResults.length > 0;
}

/**
 * Validate optimization results
 */
function validateResults(results) {
  console.log('\n📊 VALIDATION RESULTS');
  console.log('='.repeat(40));
  
  const totalTests = results.length;
  const passedTests = results.filter(r => r.passed).length;
  const failedTests = totalTests - passedTests;
  
  console.log(`📈 Total tests: ${totalTests}`);
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📊 Success rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
  
  if (failedTests > 0) {
    console.log('\n❌ Failed tests:');
    results.filter(r => !r.passed).forEach(r => {
      console.log(`   ${r.testName}: ${r.error || 'Unknown error'}`);
    });
  }
  
  console.log('='.repeat(40));
  
  return failedTests === 0;
}

/**
 * Clean up test files
 */
function cleanup() {
  console.log('\n🧹 Cleaning up test files...');
  
  if (fs.existsSync(testConfig.outputDir)) {
    const files = fs.readdirSync(testConfig.outputDir);
    files.forEach(file => {
      fs.unlinkSync(path.join(testConfig.outputDir, file));
    });
    fs.rmdirSync(testConfig.outputDir);
    console.log('✅ Cleanup complete');
  }
}

/**
 * Main test function
 */
async function runTests() {
  console.log('🧪 Enhanced Image Optimization Test Suite');
  console.log('==========================================');
  
  try {
    // Setup test environment
    const testImages = await setupTestEnvironment();
    if (!testImages) {
      console.log('❌ Test setup failed');
      process.exit(1);
    }
    
    const results = [];
    
    // Test each image
    for (let i = 0; i < testImages.length; i++) {
      const imagePath = testImages[i];
      const testName = `test-${i + 1}`;
      
      try {
        const optimizationPassed = await testOptimization(imagePath, testName);
        const responsivePassed = await testResponsiveGeneration(imagePath, testName);
        
        results.push({
          testName,
          imagePath,
          passed: optimizationPassed && responsivePassed
        });
        
      } catch (error) {
        console.error(`❌ Test ${testName} failed:`, error.message);
        results.push({
          testName,
          imagePath,
          passed: false,
          error: error.message
        });
      }
    }
    
    // Validate results
    const allTestsPassed = validateResults(results);
    
    // Cleanup
    cleanup();
    
    if (allTestsPassed) {
      console.log('\n🎉 All tests passed! Image optimization is working correctly.');
      process.exit(0);
    } else {
      console.log('\n❌ Some tests failed. Please check the optimization implementation.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
    cleanup();
    process.exit(1);
  }
}

// Run tests if called directly
if (require.main === module) {
  runTests();
}

module.exports = {
  runTests,
  testOptimization,
  testResponsiveGeneration,
  validateResults
};
