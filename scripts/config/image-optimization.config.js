/**
 * Image Optimization Configuration
 *
 * This file contains default configuration settings for the enhanced image optimization script.
 * These settings can be overridden via command line arguments.
 */

module.exports = {
  // File size targets (in KB)
  targetSizeKB: 100, // Maximum acceptable file size
  idealSizeKB: 80, // Preferred file size for optimal performance

  // Quality settings
  startingQuality: 85, // Starting quality for progressive reduction
  minQuality: 40, // Minimum quality threshold

  // Responsive image sizes (widths in pixels)
  responsiveSizes: [320, 640, 768, 1024, 1200, 1920],

  // Supported image formats
  inputFormats: ['.jpg', '.jpeg', '.png', '.webp'],

  // Output format preferences
  outputFormats: {
    primary: 'webp', // Primary format for web delivery
    fallback: 'jpeg', // Fallback format for compatibility
  },

  // Directory settings
  defaultSourceDir: 'public/images',

  // Processing options
  preserveAspectRatio: true,
  generateResponsive: false,
  generateFallbacks: false,
  skipOptimized: true,

  // Sharp optimization settings
  sharpOptions: {
    webp: {
      effort: 6, // Higher effort for better compression (0-6)
      smartSubsample: true, // Better quality at lower file sizes
      nearLossless: false, // Use lossy compression for smaller files
    },
    jpeg: {
      mozjpeg: true, // Use mozjpeg encoder for better compression
      progressive: true, // Progressive JPEG for better perceived performance
      optimizeScans: true, // Optimize scan order
    },
    png: {
      compressionLevel: 9, // Maximum compression (0-9)
      progressive: true, // Progressive PNG
      palette: true, // Use palette when beneficial
    },
  },

  // Performance settings
  concurrency: 4, // Number of images to process simultaneously

  // Logging and reporting
  verbose: false,
  generateReport: true,

  // File naming conventions
  optimizedSuffix: '-optimized',
  responsiveSuffix: 'responsive',

  // Core Web Vitals optimization targets
  coreWebVitals: {
    // LCP (Largest Contentful Paint) optimization
    lcp: {
      heroImageMaxSize: 50, // Hero images should be under 50KB
      aboveFoldMaxSize: 80, // Above-the-fold images under 80KB
      generalMaxSize: 100, // General images under 100KB
    },

    // CLS (Cumulative Layout Shift) prevention
    cls: {
      generatePlaceholders: true, // Generate low-quality placeholders
      preserveDimensions: true, // Maintain original aspect ratios
    },
  },

  // E-commerce specific settings
  ecommerce: {
    productImages: {
      thumbnail: { width: 320, targetSize: 30 }, // Product thumbnails
      medium: { width: 640, targetSize: 50 }, // Product gallery
      large: { width: 1200, targetSize: 80 }, // Product detail view
      zoom: { width: 1920, targetSize: 120 }, // Zoom/lightbox view
    },

    categoryImages: {
      card: { width: 400, targetSize: 40 }, // Category cards
      banner: { width: 1200, targetSize: 100 }, // Category banners
    },

    heroImages: {
      mobile: { width: 768, targetSize: 60 }, // Mobile hero
      desktop: { width: 1920, targetSize: 100 }, // Desktop hero
    },
  },

  // CI/CD integration settings
  cicd: {
    skipInCI: true, // Skip optimization in CI environments
    generateManifest: true, // Generate optimization manifest for tracking
    failOnError: false, // Don't fail build on optimization errors
  },
};
