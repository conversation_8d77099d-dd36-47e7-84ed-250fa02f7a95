#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * Enhanced Image Optimization Script for Next.js Ecommerce Frontend
 *
 * This script provides comprehensive image optimization with:
 * - Progressive quality reduction to meet 100KB target (ideally 50-80KB)
 * - Multiple responsive sizes generation
 * - WebP format with JPEG fallbacks
 * - Comprehensive reporting and logging
 * - <PERSON><PERSON> already optimized images
 * - Batch processing with error handling
 *
 * Usage:
 *   node scripts/optimize-images-enhanced.js [options]
 *
 * Options:
 *   --sourceDir=DIR         Source directory (default: public/images)
 *   --outputDir=DIR         Output directory (default: same as source)
 *   --targetSize=N          Target file size in KB (default: 100)
 *   --idealSize=N           Ideal file size in KB (default: 80)
 *   --quality=N             Starting quality (default: 85)
 *   --minQuality=N          Minimum quality (default: 40)
 *   --responsive            Generate responsive sizes
 *   --sizes=W1,W2,W3        Responsive widths (default: 320,640,768,1024,1200,1920)
 *   --generateFallbacks     Generate JPEG fallbacks for WebP images
 *   --skipOptimized         Skip already optimized images
 *   --dry-run               Show what would be optimized without processing
 *   --report-only           Generate report of current images without optimizing
 *   --verbose               Detailed logging
 *   --help                  Show this help message
 */

const fs = require('fs');
const path = require('path');
const {
  getFileSizeInKB,
  getFileSizeInBytes,
  fileExists,
  ensureDirectoryExists,
  getImageMetadata,
  optimizeToTargetSize,
  generateResponsiveSizes,
} = require('./utils/image-optimizer');

// Parse command line arguments
const args = process.argv.slice(2);
const getArgValue = (argName) => {
  const arg = args.find((a) => a.startsWith(`--${argName}=`));
  return arg ? arg.split('=')[1] : null;
};
const hasFlag = (flagName) => args.includes(`--${flagName}`);

// Configuration
const config = {
  sourceDir: getArgValue('sourceDir') || path.join(process.cwd(), 'public', 'images'),
  outputDir: getArgValue('outputDir') || null, // null means same as source
  targetSizeKB: parseInt(getArgValue('targetSize')) || 100,
  idealSizeKB: parseInt(getArgValue('idealSize')) || 80,
  startingQuality: parseInt(getArgValue('quality')) || 85,
  minQuality: parseInt(getArgValue('minQuality')) || 40,
  responsive: hasFlag('responsive'),
  responsiveSizes: getArgValue('sizes')
    ? getArgValue('sizes')
        .split(',')
        .map((s) => parseInt(s.trim()))
    : [320, 640, 768, 1024, 1200, 1920],
  generateFallbacks: hasFlag('generateFallbacks'),
  skipOptimized: hasFlag('skipOptimized'),
  dryRun: hasFlag('dry-run'),
  reportOnly: hasFlag('report-only'),
  verbose: hasFlag('verbose'),
  help: hasFlag('help'),
  imageExtensions: ['.jpg', '.jpeg', '.png', '.webp'],
  optimizedSuffix: '-optimized',
};

// Show help
if (config.help) {
  console.log(`
Enhanced Image Optimization Script for Next.js Ecommerce Frontend

This script provides comprehensive image optimization with:
- Progressive quality reduction to meet 100KB target (ideally 50-80KB)
- Multiple responsive sizes generation
- WebP format with JPEG fallbacks
- Comprehensive reporting and logging
- Skip already optimized images
- Batch processing with error handling

Usage:
  node scripts/optimize-images-enhanced.js [options]

Options:
  --sourceDir=DIR         Source directory (default: public/images)
  --outputDir=DIR         Output directory (default: same as source)
  --targetSize=N          Target file size in KB (default: 100)
  --idealSize=N           Ideal file size in KB (default: 80)
  --quality=N             Starting quality (default: 85)
  --minQuality=N          Minimum quality (default: 40)
  --responsive            Generate responsive sizes
  --sizes=W1,W2,W3        Responsive widths (default: 320,640,768,1024,1200,1920)
  --generateFallbacks     Generate JPEG fallbacks for WebP images
  --skipOptimized         Skip already optimized images
  --dry-run               Show what would be optimized without processing
  --report-only           Generate report of current images without optimizing
  --verbose               Detailed logging
  --help                  Show this help message

Examples:
  # Basic optimization
  pnpm optimize-enhanced

  # Generate responsive sizes with fallbacks
  pnpm optimize-enhanced -- --responsive --generateFallbacks

  # Target smaller file sizes
  pnpm optimize-enhanced -- --targetSize=50 --idealSize=30

  # Dry run to see what would be processed
  pnpm optimize-enhanced:dry-run

  # Generate report only
  pnpm optimize-enhanced:report
  `);
  process.exit(0);
}

// Validation
if (!fs.existsSync(config.sourceDir)) {
  console.error(`❌ Source directory does not exist: ${config.sourceDir}`);
  process.exit(1);
}

// Set output directory to source if not specified
if (!config.outputDir) {
  config.outputDir = config.sourceDir;
}

// Ensure output directory exists
if (!config.dryRun && !config.reportOnly) {
  ensureDirectoryExists(config.outputDir);
}

/**
 * Get all image files recursively
 * @param {string} dir - Directory to scan
 * @param {Array} fileList - Accumulator for file list
 * @returns {Array} List of image files
 */
function getImageFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);

  files.forEach((file) => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      getImageFiles(filePath, fileList);
    } else {
      const ext = path.extname(file).toLowerCase();
      if (config.imageExtensions.includes(ext)) {
        fileList.push(filePath);
      }
    }
  });

  return fileList;
}

/**
 * Check if image is already optimized
 * @param {string} filePath - Path to the image file
 * @returns {boolean} True if already optimized
 */
function isAlreadyOptimized(filePath) {
  const fileName = path.basename(filePath, path.extname(filePath));
  return fileName.includes(config.optimizedSuffix) || fileName.includes('-optimized');
}

/**
 * Generate optimization report
 * @param {Array} results - Optimization results
 */
function generateReport(results) {
  console.log('\n📊 OPTIMIZATION REPORT');
  console.log('='.repeat(50));

  const totalOriginalSize = results.reduce((sum, r) => sum + (r.originalSizeKB || 0), 0);
  const totalOptimizedSize = results.reduce((sum, r) => sum + (r.optimizedSizeKB || 0), 0);
  const totalSavings = totalOriginalSize - totalOptimizedSize;
  const avgCompressionRatio =
    results.length > 0
      ? (
          results.reduce((sum, r) => sum + parseFloat(r.compressionRatio || 0), 0) / results.length
        ).toFixed(1)
      : 0;

  console.log(`📁 Total files processed: ${results.length}`);
  console.log(`📏 Original total size: ${totalOriginalSize.toLocaleString()} KB`);
  console.log(`📦 Optimized total size: ${totalOptimizedSize.toLocaleString()} KB`);
  console.log(
    `💾 Total savings: ${totalSavings.toLocaleString()} KB (${((totalSavings / totalOriginalSize) * 100).toFixed(1)}%)`
  );
  console.log(`📊 Average compression: ${avgCompressionRatio}%`);

  const metTarget = results.filter((r) => r.metTarget).length;
  const metIdeal = results.filter((r) => r.metIdeal).length;

  console.log(
    `🎯 Met target size (${config.targetSizeKB}KB): ${metTarget}/${results.length} (${((metTarget / results.length) * 100).toFixed(1)}%)`
  );
  console.log(
    `⭐ Met ideal size (${config.idealSizeKB}KB): ${metIdeal}/${results.length} (${((metIdeal / results.length) * 100).toFixed(1)}%)`
  );

  // Show files that didn't meet target
  const missedTarget = results.filter((r) => !r.metTarget && r.success);
  if (missedTarget.length > 0) {
    console.log(`\n⚠️  Files that didn't meet target size:`);
    missedTarget.forEach((r) => {
      console.log(
        `   ${path.basename(r.filePath)}: ${r.optimizedSizeKB}KB (target: ${config.targetSizeKB}KB)`
      );
    });
  }

  console.log('='.repeat(50));
}

/**
 * Process a single image file
 * @param {string} filePath - Path to the image file
 * @returns {Promise<Object>} Processing result
 */
async function processImage(filePath) {
  const fileName = path.basename(filePath);
  const fileExt = path.extname(filePath).toLowerCase();
  const baseName = path.basename(filePath, fileExt);
  const relativePath = path.relative(config.sourceDir, filePath);
  const outputDir = path.dirname(path.join(config.outputDir, relativePath));

  // Ensure output directory exists
  if (!config.dryRun && !config.reportOnly) {
    ensureDirectoryExists(outputDir);
  }

  const result = {
    filePath,
    fileName,
    relativePath,
    originalSizeKB: getFileSizeInKB(filePath),
    success: false,
    skipped: false,
    error: null,
  };

  try {
    // Skip if already optimized and skipOptimized flag is set
    if (config.skipOptimized && isAlreadyOptimized(filePath)) {
      result.skipped = true;
      result.reason = 'Already optimized';
      if (config.verbose) {
        console.log(`⏭️  Skipping ${fileName} (already optimized)`);
      }
      return result;
    }

    // Report only mode
    if (config.reportOnly) {
      const metadata = await getImageMetadata(filePath);
      result.metadata = metadata;
      result.success = true;
      return result;
    }

    // Dry run mode
    if (config.dryRun) {
      console.log(`🔍 Would optimize: ${relativePath} (${result.originalSizeKB}KB)`);
      result.success = true;
      return result;
    }

    if (config.verbose) {
      console.log(`🔄 Processing: ${relativePath} (${result.originalSizeKB}KB)`);
    }

    // Generate WebP version
    const webpOutputPath = path.join(outputDir, `${baseName}.webp`);
    const webpResult = await optimizeToTargetSize(filePath, webpOutputPath, {
      targetSizeKB: config.targetSizeKB,
      idealSizeKB: config.idealSizeKB,
      minQuality: config.minQuality,
      maxQuality: config.startingQuality,
      format: 'webp',
    });

    if (webpResult.success) {
      Object.assign(result, webpResult);
      result.webpPath = webpOutputPath;
      result.success = true;

      if (config.verbose) {
        console.log(
          `   ✅ WebP: ${webpResult.optimizedSizeKB}KB (${webpResult.compressionRatio}% compression)`
        );
      }
    }

    // Generate JPEG fallback if requested
    if (config.generateFallbacks && webpResult.success) {
      const jpegOutputPath = path.join(outputDir, `${baseName}.jpg`);
      const jpegResult = await optimizeToTargetSize(filePath, jpegOutputPath, {
        targetSizeKB: config.targetSizeKB,
        idealSizeKB: config.idealSizeKB,
        minQuality: config.minQuality,
        maxQuality: config.startingQuality,
        format: 'jpeg',
      });

      if (jpegResult.success) {
        result.jpegPath = jpegOutputPath;
        result.jpegSizeKB = jpegResult.optimizedSizeKB;

        if (config.verbose) {
          console.log(`   ✅ JPEG fallback: ${jpegResult.optimizedSizeKB}KB`);
        }
      }
    }

    // Generate responsive sizes if requested
    if (config.responsive && webpResult.success) {
      const responsiveResults = await generateResponsiveSizes(filePath, outputDir, {
        sizes: config.responsiveSizes,
        targetSizeKB: config.targetSizeKB,
        format: 'webp',
        suffix: 'responsive',
      });

      result.responsiveImages = responsiveResults;

      if (config.verbose && responsiveResults.length > 0) {
        console.log(`   📱 Generated ${responsiveResults.length} responsive sizes`);
      }
    }
  } catch (error) {
    result.error = error.message;
    console.error(`❌ Error processing ${fileName}:`, error.message);
  }

  return result;
}

/**
 * Main processing function
 */
async function main() {
  console.log('🚀 Enhanced Image Optimization Script');
  console.log('=====================================');
  console.log(`📁 Source directory: ${config.sourceDir}`);
  console.log(`📦 Output directory: ${config.outputDir}`);
  console.log(`🎯 Target size: ${config.targetSizeKB}KB (ideal: ${config.idealSizeKB}KB)`);
  console.log(`⚙️  Quality range: ${config.minQuality}% - ${config.startingQuality}%`);

  if (config.dryRun) {
    console.log('🔍 DRY RUN MODE - No files will be modified');
  }

  if (config.reportOnly) {
    console.log('📊 REPORT ONLY MODE - Analyzing existing images');
  }

  console.log('');

  // Get all image files
  const imageFiles = getImageFiles(config.sourceDir);

  if (imageFiles.length === 0) {
    console.log('❌ No image files found in the source directory.');
    return;
  }

  console.log(`📸 Found ${imageFiles.length} image files`);
  console.log('');

  // Process images
  const results = [];
  let processed = 0;

  for (const filePath of imageFiles) {
    const result = await processImage(filePath);
    results.push(result);
    processed++;

    if (!config.verbose && !config.dryRun && !config.reportOnly) {
      const progress = ((processed / imageFiles.length) * 100).toFixed(1);
      process.stdout.write(`\r⏳ Progress: ${processed}/${imageFiles.length} (${progress}%)`);
    }
  }

  if (!config.verbose && !config.dryRun && !config.reportOnly) {
    console.log(''); // New line after progress
  }

  // Generate report
  const successfulResults = results.filter((r) => r.success && !r.skipped);
  const skippedResults = results.filter((r) => r.skipped);
  const errorResults = results.filter((r) => r.error);

  console.log('\n✅ Processing complete!');
  console.log(`📊 Processed: ${successfulResults.length}`);
  console.log(`⏭️  Skipped: ${skippedResults.length}`);
  console.log(`❌ Errors: ${errorResults.length}`);

  if (successfulResults.length > 0 && !config.dryRun) {
    generateReport(successfulResults);
  }

  if (errorResults.length > 0) {
    console.log('\n❌ Errors encountered:');
    errorResults.forEach((r) => {
      console.log(`   ${r.fileName}: ${r.error}`);
    });
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });
}

module.exports = {
  config,
  getImageFiles,
  isAlreadyOptimized,
  generateReport,
  processImage,
  main,
};
