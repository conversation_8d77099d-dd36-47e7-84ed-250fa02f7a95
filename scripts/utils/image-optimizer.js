#!/usr/bin/env node
/* eslint-disable no-console */

/**
 * Image Optimization Utilities
 * 
 * Provides utility functions for advanced image optimization with progressive quality reduction
 * to meet specific file size targets while maintaining visual quality.
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

/**
 * Get file size in KB
 * @param {string} filePath - Path to the file
 * @returns {number} File size in KB
 */
function getFileSizeInKB(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return Math.round(stats.size / 1024);
  } catch (error) {
    return 0;
  }
}

/**
 * Get file size in bytes
 * @param {string} filePath - Path to the file
 * @returns {number} File size in bytes
 */
function getFileSizeInBytes(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

/**
 * Check if file exists
 * @param {string} filePath - Path to the file
 * @returns {boolean} True if file exists
 */
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * Ensure directory exists
 * @param {string} dirPath - Directory path
 */
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * Get image metadata using Sharp
 * @param {string} filePath - Path to the image
 * @returns {Promise<Object>} Image metadata
 */
async function getImageMetadata(filePath) {
  try {
    const metadata = await sharp(filePath).metadata();
    return {
      width: metadata.width,
      height: metadata.height,
      format: metadata.format,
      channels: metadata.channels,
      density: metadata.density,
      hasAlpha: metadata.hasAlpha,
      size: getFileSizeInBytes(filePath)
    };
  } catch (error) {
    console.error(`Error getting metadata for ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Progressive quality optimization to meet target file size
 * @param {string} inputPath - Input image path
 * @param {string} outputPath - Output image path
 * @param {Object} options - Optimization options
 * @returns {Promise<Object>} Optimization result
 */
async function optimizeToTargetSize(inputPath, outputPath, options = {}) {
  const {
    targetSizeKB = 100,
    idealSizeKB = 80,
    minQuality = 40,
    maxQuality = 85,
    format = 'webp',
    width = null,
    height = null,
    preserveAspectRatio = true
  } = options;

  const targetSizeBytes = targetSizeKB * 1024;
  const idealSizeBytes = idealSizeKB * 1024;
  
  let currentQuality = maxQuality;
  let bestResult = null;
  let attempts = 0;
  const maxAttempts = 10;

  const originalSize = getFileSizeInBytes(inputPath);
  
  while (attempts < maxAttempts && currentQuality >= minQuality) {
    try {
      let sharpInstance = sharp(inputPath);
      
      // Apply resizing if specified
      if (width || height) {
        const resizeOptions = { 
          fit: preserveAspectRatio ? 'inside' : 'fill',
          withoutEnlargement: true
        };
        
        if (width && height) {
          sharpInstance = sharpInstance.resize(width, height, resizeOptions);
        } else if (width) {
          sharpInstance = sharpInstance.resize(width, null, resizeOptions);
        } else if (height) {
          sharpInstance = sharpInstance.resize(null, height, resizeOptions);
        }
      }

      // Apply format-specific optimization
      if (format === 'webp') {
        sharpInstance = sharpInstance.webp({ 
          quality: currentQuality,
          effort: 6, // Higher effort for better compression
          smartSubsample: true
        });
      } else if (format === 'jpeg') {
        sharpInstance = sharpInstance.jpeg({ 
          quality: currentQuality,
          mozjpeg: true,
          progressive: true
        });
      } else if (format === 'png') {
        sharpInstance = sharpInstance.png({ 
          quality: currentQuality,
          compressionLevel: 9,
          progressive: true
        });
      }

      const buffer = await sharpInstance.toBuffer();
      const resultSize = buffer.length;
      
      // Save current result
      const result = {
        quality: currentQuality,
        size: resultSize,
        sizeKB: Math.round(resultSize / 1024),
        compressionRatio: ((originalSize - resultSize) / originalSize * 100).toFixed(1),
        buffer
      };

      // Check if we've found an ideal size
      if (resultSize <= idealSizeBytes) {
        bestResult = result;
        break;
      }
      
      // Check if we're within target size
      if (resultSize <= targetSizeBytes) {
        if (!bestResult || resultSize > bestResult.size) {
          bestResult = result;
        }
      }
      
      // If we haven't found a good result yet, keep the current one as best
      if (!bestResult) {
        bestResult = result;
      }

      attempts++;
      currentQuality -= 5; // Reduce quality by 5% each iteration
      
    } catch (error) {
      console.error(`Error optimizing at quality ${currentQuality}:`, error.message);
      break;
    }
  }

  if (bestResult) {
    // Write the best result to file
    fs.writeFileSync(outputPath, bestResult.buffer);
    
    return {
      success: true,
      originalSize: originalSize,
      optimizedSize: bestResult.size,
      originalSizeKB: Math.round(originalSize / 1024),
      optimizedSizeKB: bestResult.sizeKB,
      quality: bestResult.quality,
      compressionRatio: bestResult.compressionRatio,
      attempts: attempts,
      metTarget: bestResult.size <= targetSizeBytes,
      metIdeal: bestResult.size <= idealSizeBytes
    };
  }

  return {
    success: false,
    error: 'Could not optimize image to target size',
    originalSize: originalSize,
    originalSizeKB: Math.round(originalSize / 1024)
  };
}

/**
 * Generate responsive image sizes
 * @param {string} inputPath - Input image path
 * @param {string} outputDir - Output directory
 * @param {Object} options - Generation options
 * @returns {Promise<Array>} Array of generated images info
 */
async function generateResponsiveSizes(inputPath, outputDir, options = {}) {
  const {
    sizes = [320, 640, 768, 1024, 1200, 1920],
    targetSizeKB = 100,
    format = 'webp',
    suffix = ''
  } = options;

  const results = [];
  const originalMetadata = await getImageMetadata(inputPath);
  
  if (!originalMetadata) {
    return results;
  }

  const baseName = path.basename(inputPath, path.extname(inputPath));
  const originalWidth = originalMetadata.width;

  for (const size of sizes) {
    // Skip if the target size is larger than the original
    if (size > originalWidth) {
      continue;
    }

    const outputFileName = `${baseName}${suffix ? `-${suffix}` : ''}-${size}w.${format}`;
    const outputPath = path.join(outputDir, outputFileName);

    try {
      const result = await optimizeToTargetSize(inputPath, outputPath, {
        targetSizeKB,
        width: size,
        format,
        preserveAspectRatio: true
      });

      if (result.success) {
        results.push({
          width: size,
          fileName: outputFileName,
          path: outputPath,
          ...result
        });
      }
    } catch (error) {
      console.error(`Error generating ${size}w version:`, error.message);
    }
  }

  return results;
}

module.exports = {
  getFileSizeInKB,
  getFileSizeInBytes,
  fileExists,
  ensureDirectoryExists,
  getImageMetadata,
  optimizeToTargetSize,
  generateResponsiveSizes
};
